# 🔋 Voltifi API

A versioned REST API for Voltifi - On-demand EV charging and mobile tyre support services.

## Features

- **API Versioning**: Proper API versioning (v1) with structured routing
- **User Roles**: Support for "supplier" and "customer" roles
- **Authentication**: Secure Laravel Sanctum token-based authentication
- **Rate Limiting**: Configurable rate limits for different endpoint types
- **Validation**: Comprehensive request validation with custom error messages
- **Documentation**: Complete API documentation with examples
- **Testing**: Full test coverage for all endpoints
- **Security**: CORS configuration, input sanitization, and security best practices

## API Endpoints

### Authentication
- `POST /api/v1/register` - Register new user
- `POST /api/v1/login` - User login
- `POST /api/v1/logout` - User logout (authenticated)
- `GET /api/v1/user` - Get user profile (authenticated)

### Utility
- `GET /api/v1/health` - API health check

## User Roles

- **supplier** - Service providers offering EV charging and mobile tyre services
- **customer** - End users requesting on-demand services

## Installation & Setup

### Prerequisites
- PHP 8.2+
- Composer
- Node.js & NPM
- SQLite/MySQL/PostgreSQL

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd voltifi-api
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install Node.js dependencies**
   ```bash
   npm install
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Database setup**
   ```bash
   # For SQLite (default)
   touch database/database.sqlite
   
   # Run migrations
   php artisan migrate
   ```

6. **Generate Sanctum secret key**
   ```bash
   php artisan sanctum:install
   ```

### Development

1. **Start the development server**
   ```bash
   composer run dev
   ```
   This will start:
   - Laravel development server (port 8000)
   - Queue worker
   - Vite development server

2. **Run tests**
   ```bash
   php artisan test
   # or
   composer run test
   ```

## API Documentation

### Online Documentation
Visit `/api-docs.html` in your browser for complete API documentation with examples.

### Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer {your-token}
```

### Response Format
All API responses follow this consistent format:
```json
{
  "success": true|false,
  "message": "Description of the result",
  "data": { ... },      // Present on success
  "errors": { ... }     // Present on validation errors
}
```

## Usage Examples

### Register a new customer
```bash
curl -X POST http://localhost:8000/api/v1/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "role": "customer",
    "password": "password123",
    "password_confirmation": "password123"
  }'
```

### Login
```bash
curl -X POST http://localhost:8000/api/v1/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Get user profile
```bash
curl -X GET http://localhost:8000/api/v1/user \
  -H "Authorization: Bearer {your-token}" \
  -H "Content-Type: application/json"
```

## Rate Limits

- **Authentication endpoints**: 5 requests per minute
- **General API endpoints**: 60 requests per minute

Rate limits are applied per user (if authenticated) or per IP address (for anonymous requests).

## Security Features

- **Input Validation**: All inputs are validated and sanitized
- **Rate Limiting**: Prevents abuse with configurable rate limits
- **CORS**: Properly configured for mobile app access
- **Token Authentication**: Secure Sanctum token-based authentication
- **Password Hashing**: Bcrypt password hashing
- **SQL Injection Protection**: Laravel's query builder provides protection

## Testing

The API includes comprehensive tests covering:
- User registration (both roles)
- Authentication (login/logout)
- Input validation
- Error handling
- Rate limiting
- Security

Run tests with:
```bash
php artisan test tests/Feature/Api/V1/AuthTest.php
```

## Project Structure

```
app/
├── Http/
│   ├── Controllers/Api/V1/
│   │   └── AuthController.php
│   ├── Requests/Api/V1/
│   │   ├── LoginRequest.php
│   │   └── RegisterRequest.php
│   ├── Middleware/
│   │   └── ApiRateLimitMiddleware.php
│   └── Traits/
│       └── ApiResponseTrait.php
├── Models/
│   └── User.php
database/
├── migrations/
│   └── 2025_05_25_061414_add_phone_and_role_to_users_table.php
└── factories/
    └── UserFactory.php
routes/
└── api.php
tests/
└── Feature/Api/V1/
    └── AuthTest.php
public/
└── api-docs.html
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support or questions about the Voltifi API, please contact the development team.
