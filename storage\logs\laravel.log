[2025-05-25 06:03:24] local.ERROR: Command "migrate:rollack" is not defined.

Did you mean one of these?
    migrate
    migrate:fresh
    migrate:install
    migrate:refresh
    migrate:reset
    migrate:rollback
    migrate:status {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"migrate:rollack\" is not defined.

Did you mean one of these?
    migrate
    migrate:fresh
    migrate:install
    migrate:refresh
    migrate:reset
    migrate:rollback
    migrate:status at E:\\websites\\voltifi-api\\vendor\\symfony\\console\\Application.php:726)
[stacktrace]
#0 E:\\websites\\voltifi-api\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('migrate:rollack')
#1 E:\\websites\\voltifi-api\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 E:\\websites\\voltifi-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 E:\\websites\\voltifi-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 E:\\websites\\voltifi-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
